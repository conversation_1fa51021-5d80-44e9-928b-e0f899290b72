from django.shortcuts import render
import pandas as pd
import pymysql  # 修改1：替换MySQLdb
import io
from django.http import HttpResponse
from urllib.parse import quote
from datetime import datetime
from django.core.exceptions import ValidationError
from django.conf import settings
from django.db import connection
from django.db import DatabaseError  # 新增导入
import cx_Oracle
from django.contrib.auth.decorators import login_required
import os
from django.contrib.auth.forms import AuthenticationForm
from django.shortcuts import render
from django.http import JsonResponse

# 公共数据库连接函数（新增）
def get_mysql_connection():
    return pymysql.connect(
        **settings.MYSQL_CONFIG,
        cursorclass=pymysql.cursors.DictCursor  # 获取字典格式结果
    )

@login_required
def index(request):
    return render(request, 'index.html')
    
@login_required
def kq(request):
    if request.method == 'POST':
        # 获取查询条件
        params = {
            'start_date': request.POST.get('start_date'),
            'end_date': request.POST.get('end_date'),
            'name': request.POST.get('name'),
            'id_number': request.POST.get('id_number')
        }

        try:
            # 使用 Django 内置 connection 管理连接（修改）
            with connection.cursor() as cursor:
                # 构造安全查询（防止SQL注入）
                sql = "SELECT * FROM 考勤 WHERE 1=1"
                conditions = []
                values = []

                if params['start_date']:
                    conditions.append("时间 >= %s")
                    values.append(params['start_date'])
                if params['end_date']:
                    conditions.append("时间 < DATE_ADD(%s, INTERVAL 1 DAY)")
                    values.append(params['end_date'])
                if params['name']:
                    conditions.append("姓名 = %s")
                    values.append(params['name'])
                if params['id_number']:
                    conditions.append("证件号 = %s")
                    values.append(params['id_number'])

                if conditions:
                    sql += " AND " + " AND ".join(conditions)
                
                sql += " ORDER BY 证件号, 时间"
                
                cursor.execute(sql, values)
                results = cursor.fetchall()

        except DatabaseError as e:  # 改为 Django 数据库异常
            return HttpResponse(f"数据库错误: {e}", status=500)

        # 转换为DataFrame（调整列名匹配实际字段）
        df = pd.DataFrame(results)
        if not df.empty:
            df.columns = ['记录号', '姓名', '证件号', '时间', '校区', '异常情况', '星期']

        # 处理下载请求
        if 'download_url' in request.POST:
            buffer = io.BytesIO()
            df.to_excel(buffer, index=False)
            buffer.seek(0)

            file_name = f"tsgkaoqin_{params['start_date']}_{params['end_date']}_{quote(params['name'])}_{params['id_number']}.xlsx"
            
            response = HttpResponse(
                buffer,
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response

        # 返回结果
        context = {
            'table_html': df.to_html(index=False) if not df.empty else "",
            'show_results': True,
            'kq_results': results,
            **params
        }
        return render(request, 'kq.html', context)

    return render(request, 'kq.html')




@login_required 
def kqtoday(request):
    # 修改Oracle连接字符串
    connection = cx_Oracle.connect(
        f"{os.getenv('ORACLE_USER')}/{os.getenv('ORACLE_PASSWORD')}"
        f"@{os.getenv('ORACLE_HOST')}:1521/{os.getenv('ORACLE_SERVICE_NAME')}"
    )
    
    # Create a cursor
    cursor = connection.cursor()

    # Execute the query
    query = """
        SELECT
           a.RecNum,
            a.OcurrTime as 时间,
            b.PERCODE,
            b.ACCNAME,
            a.DoorNum
        FROM
            easytong.AC_EventRecord a,
            easytong.AM_ACCOUNT b,
            easytong.SC_AccDep c
        WHERE
            a.AccNum = b.AccNum
            AND b.AccDepID = c.AccDepID
            AND a.EventID = 9
            AND a.DoorNum IN (245, 242, 272, 269, 275, 278, 250, 252, 281)
            AND c.AccDepName LIKE '图书馆%'
            AND TRUNC(a.OcurrTime) = TRUNC(SYSDATE)
        ORDER BY
            a.OcurrTime
    """
    cursor.execute(query)

    # Fetch all the results
    results = cursor.fetchall()

    # Close the cursor and database connection
    cursor.close()
    connection.close()

    # Return the kq results to the template
    context = {
        'kqtoday_results': results
    }
    return render(request, 'kqtoday.html', context)


@login_required
def mj(request):
    if request.method == 'POST':
        params = {
            'start_date': request.POST.get('start_date'),
            'end_date': request.POST.get('end_date'),
            'name': request.POST.get('name'),
            'id_number': request.POST.get('id_number'),
            'category': request.POST.get('category')
        }

        # 新增日期验证
        try:
            if params['start_date']:
                datetime.strptime(params['start_date'], '%Y-%m-%d')
            if params['end_date']:
                datetime.strptime(params['end_date'], '%Y-%m-%d')
        except ValueError:
            return HttpResponse("无效的日期格式，请使用YYYY-MM-DD格式", status=400)

        try:
            # 使用 Django 内置 connection 管理连接（修改）
            with connection.cursor() as cursor:
                # 参数化查询
                sql = """SELECT 学号,姓名,时间,学院,通道,通行方向,校区,专业,年级组 
                        FROM t_accessrecord WHERE 1=1"""
                conditions = []
                values = []

                if params['start_date']:
                    conditions.append("时间 >= %s")
                    values.append(params['start_date'])
                if params['end_date']:
                    conditions.append("时间 < DATE_ADD(%s, INTERVAL 1 DAY)")
                    values.append(params['end_date'])
                if params['name']:
                    conditions.append("姓名 = %s")
                    values.append(params['name'])
                if params['id_number']:
                    conditions.append("学号 = %s")
                    values.append(params['id_number'])
                if params['category']:
                    conditions.append("学院 LIKE CONCAT(%s, '%')")
                    values.append(params['category'])

                if conditions:
                    sql += " AND " + " AND ".join(conditions)
                
                sql += " ORDER BY 时间"
                
                cursor.execute(sql, values)
                results = cursor.fetchall()

        except DatabaseError as e:  # 改为 Django 数据库异常
            return HttpResponse(f"数据库错误: {e}", status=500)

        # 处理数据
        # 原代码：df = pd.DataFrame(results) if results else pd.DataFrame()
        # 原代码：if not df.empty and len(df.columns) == 9:
        # 原代码：    df.columns = ['学号','姓名','时间','学院','通道','通行方向','校区','专业','年级组']
        
        # 修改后：直接在初始化时指定列名（非空时用数据填充，空时创建带列名的空表）
        df = pd.DataFrame(results, columns=['学号','姓名','时间','学院','通道','通行方向','校区','专业','年级组']) if results else pd.DataFrame(columns=['学号','姓名','时间','学院','通道','通行方向','校区','专业','年级组'])

        # 处理下载请求
        if 'download_url' in request.POST:
            buffer = io.BytesIO()
            df.to_excel(buffer, index=False)
            buffer.seek(0)

            file_name = f"menjin_{params['start_date']}_{params['end_date']}_{quote(params['name'])}_{params['id_number']}.xlsx"
            
            response = HttpResponse(
                buffer,
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response

        context = {
            'table_html': df.to_html(index=False) if not df.empty else "",
            'show_results': True,
            'mj_results': results,
            **params
        }
        return render(request, 'mj.html', context)
        
    return render(request, 'mj.html')

@login_required
def stats(request):
    if request.method == 'POST':
        # 处理原有分组统计
        if 'start_date' in request.POST:  
            params = {
                'start_date': request.POST.get('start_date'),
                'end_date': request.POST.get('end_date'),
                'group_by': request.POST.get('group_by', '学院'),
                'direction': request.POST.get('direction'),
                'campus': request.POST.get('campus')
            }
            
            # 验证日期格式
            try:
                if params['start_date']:
                    datetime.strptime(params['start_date'], '%Y-%m-%d')
                if params['end_date']:
                    datetime.strptime(params['end_date'], '%Y-%m-%d')
            except ValueError:
                return HttpResponse("无效的日期格式，请使用YYYY-MM-DD格式", status=400)
        
            try:
                with get_mysql_connection() as db:
                    with db.cursor() as cursor:
                        group_field = params['group_by']
                        sql = """
                            SELECT 
                                {group_field} AS group_field,
                                SUM(CASE WHEN 年级组 IS NOT NULL THEN 1 ELSE 0 END) AS student_count,
                                SUM(CASE WHEN 年级组 IS NULL THEN 1 ELSE 0 END) AS teacher_count,
                                COUNT(*) AS total_count
                            FROM t_accessrecord
                            WHERE 1=1
                        """.format(group_field=group_field)
                        
                        conditions = []
                        values = []
        
                        if params['start_date']:
                            conditions.append("时间 >= %s")
                            values.append(params['start_date'])
                        if params['end_date']:
                            conditions.append("时间 < DATE_ADD(%s, INTERVAL 1 DAY)")
                            values.append(params['end_date'])
                        if params['direction']:
                            conditions.append("通行方向 = %s")
                            values.append(params['direction'])
                        if params['campus']:
                            conditions.append("校区 = %s")
                            values.append(params['campus'])
        
                        if conditions:
                            sql += " AND " + " AND ".join(conditions)
                        
                        sql += f" GROUP BY {group_field} ORDER BY total_count DESC"
                        
                        cursor.execute(sql, values)
                        stats_results = cursor.fetchall()
        
                return render(request, 'stats.html', {
                    'show_results': True,
                    'stats_results': stats_results,
                    'start_date': params['start_date'],
                    'end_date': params['end_date'],
                    'group_by': params['group_by'],
                    'direction': params['direction'],
                    'campus': params['campus']
                })

            except pymysql.Error as e:
                return HttpResponse(f"数据库错误: {e}", status=500)

        # 处理学院专业统计（合并校区）
        elif 'college_year' in request.POST:
            year = request.POST.get('college_year')
            try:
                if not year.isdigit() or len(year) != 4:
                    raise ValueError
                year_int = int(year)
                if not (2000 <= year_int <= 2100):
                    raise ValueError
            except ValueError:
                return HttpResponse("请输入有效的四位年份(2000-2100)", status=400)

            try:
                with get_mysql_connection() as db:
                    with db.cursor() as cursor:
                        # 修改SQL查询，移除校区条件和分组
                        college_sql = """
                            SELECT 学院,专业,COUNT(*) as 次数 
                            FROM t_accessrecord 
                            WHERE 通行方向 = '进'
                            AND 年级组 IS NOT NULL
                            AND 学院 IS NOT NULL
                            AND SUBSTR(时间, 1, 4) = %s
                            GROUP BY 学院,专业
                            ORDER BY 次数 DESC
                        """
                        cursor.execute(college_sql, (year,))
                        college_stats = cursor.fetchall()

                return render(request, 'stats.html', {
                    'show_college_stats': True,
                    'college_year': year,
                    'college_stats': college_stats
                })

            except pymysql.Error as e:
                return HttpResponse(f"数据库错误: {e}", status=500)

        # 恢复年度按月统计逻辑
        elif 'year' in request.POST:  
            year = request.POST.get('year')
            try:
                # 验证年份格式
                if not year.isdigit() or len(year) != 4:
                    raise ValueError
                year = int(year)
                if not (2000 <= year <= 2100):
                    raise ValueError
            except ValueError:
                return HttpResponse("请输入有效的四位年份(2000-2100)", status=400)

            try:
                with get_mysql_connection() as db:
                    with db.cursor() as cursor:
                        # 简化后的SQL查询
                        sql = """
                            SELECT 
                                SUBSTR(时间, 1, 7) AS month,
                                COUNT(*) AS total_count
                            FROM t_accessrecord
                            WHERE SUBSTR(时间, 1, 4) = %s
                            and 通行方向 = '进'
                            GROUP BY month
                            ORDER BY month
                        """
                        cursor.execute(sql, (str(year),))
                        
                        # 处理查询结果
                        raw_stats = cursor.fetchall()
                        
                        # 转换为字典格式 {月份: 数量}
                        stats_dict = {row['month']: row['total_count'] for row in raw_stats}
                        
                        # 生成完整月份数据
                        full_stats = []
                        for m in range(1, 13):
                            month_str = f"{year}-{m:02d}"
                            full_stats.append({
                                'month': month_str,
                                'count': stats_dict.get(month_str, 0)
                            })
                        
                        # 计算总人次
                        total_count = sum(item['count'] for item in full_stats)

            except pymysql.Error as e:
                return HttpResponse(f"数据库错误: {e}", status=500)

            return render(request, 'stats.html', {
                'year': year,
                'monthly_stats': full_stats,
                'total_count': total_count,
                'show_yearly_stats': True  # 确保该标志存在
            })
        
        # 处理年度入馆排名
        elif 'rank_year' in request.POST:
            year = request.POST.get('rank_year')
            try:
                if not year.isdigit() or len(year) != 4:
                    raise ValueError
                year_int = int(year)
                if not (2000 <= year_int <= 2100):
                    raise ValueError
            except ValueError:
                return HttpResponse("请输入有效的四位年份(2000-2100)", status=400)

            try:
                with get_mysql_connection() as db:
                    with db.cursor() as cursor:
                        # 东湖校区排名
                        east_lake_sql = """
                            SELECT 姓名,年级组,专业,COUNT(*) as 次数 
                            FROM t_accessrecord 
                            WHERE 校区='东湖' 
                            AND 通行方向='进'
                            AND SUBSTR(时间, 1, 4) = %s
                            GROUP BY 姓名,年级组,专业
                            ORDER BY 次数 DESC
                            LIMIT 20
                        """
                        cursor.execute(east_lake_sql, (year,))
                        east_lake_rank = cursor.fetchall()

                        # 东南校区排名
                        south_east_sql = """
                            SELECT 姓名,年级组,专业,COUNT(*) as 次数 
                            FROM t_accessrecord 
                            WHERE 校区='东南' 
                            AND 通行方向='进'
                            AND SUBSTR(时间, 1, 4) = %s
                            GROUP BY 姓名,年级组,专业
                            ORDER BY 次数 DESC
                            LIMIT 20
                        """
                        cursor.execute(south_east_sql, (year,))
                        south_east_rank = cursor.fetchall()

                return render(request, 'stats.html', {
                    'show_rank': True,
                    'rank_year': year,
                    'east_lake_rank': east_lake_rank,
                    'south_east_rank': south_east_rank
                })

            except pymysql.Error as e:
                return HttpResponse(f"数据库错误: {e}", status=500)

            except pymysql.Error as e:
                return HttpResponse(f"数据库错误: {e}", status=500)

            return render(request, 'stats.html', {
                'year': year,
                'monthly_stats': full_stats,
                'total_count': total_count,
                'show_yearly_stats': True  # 确保该标志存在
            })
        
        # 其他统计逻辑保持不变 ...
    
    # 明确处理GET请求
    if request.method == 'POST':
        try:
            # 处理表单数据
            data = request.POST
            
            # 数据处理逻辑...
            
            # 返回成功响应
            return JsonResponse({'status': 'success', 'data': processed_data})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=400)
    
    # GET请求处理
    return render(request, 'stats.html')


def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            # 处理登录逻辑
            pass
    else:
        form = AuthenticationForm()
    return render(request, 'registration/login.html', {'form': form})