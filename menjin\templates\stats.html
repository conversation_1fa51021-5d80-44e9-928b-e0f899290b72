<!DOCTYPE html>
{% load static %}
<html>

<head>
    <title>门禁通行统计</title>
    <script src="{% static 'jquery-3.6.0.min.js' %}"></script>
    <link rel="stylesheet" href="{% static 'layui/css/layui.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}">
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <style>
        .layui-nav {
            background: none;
            border: none;
            padding: 0;
        }
        .layui-nav .layui-nav-item {
            margin: 0 10px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .layui-nav .layui-nav-item a {
            color: #333;
            padding: 5px 20px;
            text-decoration: none;
            border-radius: 5px;
        }
        .layui-nav .layui-nav-item:hover a {
            background-color: #f2f2f2;
            color: #0482bd;
        }
        .layui-nav .layui-nav-item.layui-this a {
            background-color: #0482bd;
            color: white !important;
        }
        .layui-nav .layui-nav-item .layui-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body class="layui-layout-body">
    <!-- 登录状态 -->
    <div class="login-status" style="position: absolute; top: 20px; right: 20px; color: #666; z-index: 999;">
        {% if user.is_authenticated %}
            欢迎，{{ user.username }} | <a href="{% url 'logout' %}" style="color: #009688;">退出</a>
        {% else %}
            <a href="{% url 'login' %}" style="color: #009688;">登录</a>
        {% endif %}
    </div>
    <div class="layui-container">
        <!-- 系统总标题 -->
        <div class="layui-card" style="margin-bottom: 10px;">
            <div class="layui-card-header" style="background-color: #2F4056; text-align: center; padding: 15px;">
                <h1 style="color: white; margin: 0; font-size: 24px;">图书馆门禁查询系统</h1>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="layui-card" style="margin-bottom: 15px;">
            <div class="layui-card-body" style="padding: 10px;">
                <ul class="layui-nav" lay-filter="">
                    <li class="layui-nav-item">
                        <a href="{% url 'mj' %}">
                            <i class="layui-icon layui-icon-search"></i> 图书馆门禁通行记录查询系统
                        </a>
                    </li>
                    <li class="layui-nav-item">
                        <a href="{% url 'kq' %}">
                            <i class="layui-icon layui-icon-user"></i> 图书馆馆员考勤查询
                        </a>
                    </li>
                    <li class="layui-nav-item layui-this">
                        <a href="{% url 'stats' %}">
                            <i class="layui-icon layui-icon-chart"></i> 门禁通行人次统计
                        </a>
                    </li>
                </ul>
            </div>
        </div>


        <div>
            <div class="layui-card">
                <div class="layui-card-header">查询条件</div>
                <div class="layui-card-body">
                    <form class="layui-form" method="post" action="{% url 'stats' %}">
                        {% csrf_token %}
                        <div class="layui-form-item">
                            <label class="layui-form-label">起始日期:</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" name="start_date" id="start_date" placeholder="例如：2024-01-01" value="{{ start_date }}" required>
                            </div>
                            <label class="layui-form-label">截止日期:</label>
                            <div class="layui-input-inline"><input type="text" class="layui-input"  name="end_date" id="end_date" placeholder="例如：2024-02-01" value="{{ end_date }}"></div> 
                            <br> <br>
                            <div class="layui-form-item">
                                <div class="layui-inline">
                                    <label class="layui-form-label">统计维度:</label>
                                    <div class="layui-input-inline">
                                        <select name="group_by" id="group_by">
                                            <option value="校区" {% if group_by == '校区' %}selected{% endif %}>按校区</option>
                                            <option value="年级组" {% if group_by == '年级组' %}selected{% endif %}>按年级</option>                
                                            <option value="学院" {% if group_by == '学院' %}selected{% endif %}>按学院</option>
                                            <option value="专业" {% if group_by == '专业' %}selected{% endif %}>按专业</option>           
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">通行方向:</label>
                                    <div class="layui-input-inline">
                                        <select name="direction" id="direction">
                                            <option value="">全部</option>
                                            <option value="进" {% if direction == '进' %}selected{% endif %}>进</option>
                                            <option value="出" {% if direction == '出' %}selected{% endif %}>出</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline">
                                    <label class="layui-form-label">校区:</label>
                                    <div class="layui-input-inline">
                                        <select name="campus" id="campus">
                                            <option value="">全部</option>
                                            <option value="东湖" {% if campus == '东湖' %}selected{% endif %}>东湖</option>
                                            <option value="东南" {% if campus == '东南' %}selected{% endif %}>东南</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn" lay-submit lay-filter="formDemo">统计</button>
                                    </div>
                                </div>
                            </div>
                            
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </div>
    <br>

    <div class="layui-container">
    <div class="layui-card" style="margin: 20px auto; text-align: center;">
        
       <form method="post" action="{% url 'stats' %}" style="margin: 0;">
            {% csrf_token %}
            <label for="rank_year">年度入馆排名:</label>
            <input type="text" name="rank_year" id="rank_year" 
                   placeholder="例如：2024" value="{{ rank_year }}">
            <button class="layui-btn" lay-submit lay-filter="formDemo">查询</button>
        </form>
    <br>
        <!-- 其他表单也做同样修改 -->
        <form method="post" action="{% url 'stats' %}" style="margin: 0;">
            {% csrf_token %}
            <label for="year">年度按月统计:</label>
            <input type="text" name="year" id="year" placeholder="例如：2024" value="{{ year }}">
            <button class="layui-btn" lay-submit lay-filter="formDemo">查询</button>
        </form>
</br>
        <form method="post" action="{% url 'stats' %}" style="margin: 0;">
            {% csrf_token %}
            <label for="college_year">按学院专业统计:</label>
            <input type="text" name="college_year" id="college_year" 
                   placeholder="例如：2024" value="{{ college_year }}">
          <button class="layui-btn" lay-submit lay-filter="formDemo">查询</button>
        </form>
    </div>
</div>
       

<div class="layui-container">
    <!-- 学院专业统计 -->
    {% if show_college_stats %}
    <div class="layui-card" style="margin: 20px auto; max-width: 90%; overflow: auto;">  <!-- 添加overflow:auto -->
        <div class="layui-card-header">{{ college_year }}年学院专业入馆统计</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-size="sm" style="min-width: 800px;">  <!-- 添加最小宽度 -->
                <colgroup>
                    <col >
                    <col >
                    <col>
                </colgroup>
                <thead>
                    <tr style="background-color: #008000; color: white;">  <!-- 添加绿色表头 -->
                        <th>学院</th>
                        <th>专业</th>
                        <th>入馆次数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in college_stats %}
                    <tr>
                        <td>{{ item.学院 }}</td>
                        <td>{{ item.专业 }}</td>
                        <td>{{ item.次数 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- 月度统计结果 -->
    {% if show_yearly_stats %}
    <div class="layui-card" style="margin: 20px auto; max-width: 90%; overflow: auto;">  <!-- 添加overflow:auto -->
        <div class="layui-card-header">{{ year }}年每月入馆人次统计 (总人次: {{ total_count }})</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-size="sm" style="min-width: 800px;">  <!-- 添加最小宽度 -->
                <colgroup>
                    <col >
                    <col>
                </colgroup>
                <thead>
                    <tr style="background-color: #008000; color: white;">  <!-- 添加绿色表头 -->
                        <th>月份</th>
                        <th>通行人次</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in monthly_stats %}
                    <tr>
                        <td>{{ item.month }}</td>
                        <td>{{ item.count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- 校区排名结果 -->
    {% if show_rank %}
    <div class="layui-card" style="margin: 20px auto; max-width: 90%; overflow: auto;">  <!-- 添加overflow:auto -->
        <div class="layui-card-header">{{ rank_year }}年东湖校区个人入馆排名TOP20</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-size="sm" style="min-width: 800px;">  <!-- 添加最小宽度 -->
                <colgroup>
                    <col >
                    <col >
                    <col >
                    <col>
                </colgroup>
                <thead>
                    <tr style="background-color: #008000; color: white;">  <!-- 添加绿色表头 -->
                        <th>姓名</th>
                        <th>年级组</th>
                        <th>专业</th>
                        <th>入馆次数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in east_lake_rank %}
                    <tr>
                        <td>{{ item.姓名 }}</td>
                        <td>{{ item.年级组 }}</td>
                        <td>{{ item.专业 }}</td>
                        <td>{{ item.次数 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="layui-card" style="margin: 20px auto; max-width: 90%; overflow: auto;">  <!-- 调整为统一居中样式 -->
        <div class="layui-card-header">{{ rank_year }}年东南校区个人入馆排名TOP20</div>
        <div class="layui-card-body">
            <table class="layui-table" lay-size="sm" style="min-width: 800px;">  <!-- 添加最小宽度 -->
                <colgroup>
                    <col >
                    <col  >
                    <col >
                    <col  >
                </colgroup>
                <thead>
                    <tr style="background-color: #008000; color: white;">  <!-- 添加绿色表头 -->
                        <th>姓名</th>
                        <th>年级组</th>
                        <th>专业</th>
                        <th>入馆次数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in south_east_rank %}
                    <tr>
                        <td>{{ item.姓名 }}</td>
                        <td>{{ item.年级组 }}</td>
                        <td>{{ item.专业 }}</td>
                        <td>{{ item.次数 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

    <!-- 在页面底部添加脚本 -->
    <script src="{% static 'layui/layui.js' %}"></script>
    <script>
    layui.use(['form', 'layer', 'laydate', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var element = layui.element;
        
        // 日期控件初始化
        laydate.render({
            elem: '#start_date'
        });
        laydate.render({
            elem: '#end_date'
        });
        
        // 表单验证
        form.verify({
            // 自定义验证规则
        });
    });
    </script>
 <script>
    $(document).ready(function() {
        // 创建遮罩层元素
        var $mask = $('<div class="loading-mask">查询中...</div>').appendTo('body');
        
        $('form').on('submit', function(e) {
            // 阻止表单默认提交行为
            e.preventDefault();
            
            // 显示遮罩层
            $mask.show();
            
            // 禁用所有表单按钮
            $('button[type="submit"]').prop('disabled', true);
            
            // 使用AJAX提交表单
            $.ajax({
                type: $(this).attr('method'),
                url: $(this).attr('action'),
                data: $(this).serialize(),
                headers: {
                    "X-CSRFToken": $('input[name="csrfmiddlewaretoken"]').val()
                },
                success: function(response) {
                    $('body').html(response);
                },
                error: function(xhr, status, error) {
                    $mask.hide();
                    $('button[type="submit"]').prop('disabled', false);
                    console.error("Error:", error);
                    alert('提交失败: ' + (xhr.responseText || '服务器错误'));
                }
            });
        });
            // 页面加载完成后清空所有输入框
        $('input[type="text"]').val('');
    });
    </script>

<div class="layui-container">
<!-- 修改所有表格容器和表格类，确保每个结果只显示一次 -->
{% if show_results %}
<div class="layui-card" style="margin: 20px auto; max-width: 90%; overflow: auto;">  <!-- 添加overflow:auto -->
    <div class="layui-card-body">
        <h3>统计结果 ({{ start_date }} 至 {{ end_date }}{% if campus %}，校区：{{ campus }}{% endif %})</h3>
        <table class="layui-table" lay-size="sm" style="min-width: 600px;">  <!-- 添加最小宽度 -->
            <thead>
                <tr style="background-color: #008000; color: white;">  <!-- 表头添加绿色底色和白色文字 -->
                    <th>{{ group_by }}</th>
                    <th>学生人次</th>
                    <th>教师人次</th>
                    <th>总人次</th>
                </tr>
            </thead>
            <tbody>
                {% for result in stats_results %}
                <tr>
                    <td>{{ result.group_field }}</td>
                    <td>{{ result.student_count }}</td>
                    <td>{{ result.teacher_count }}</td>
                    <td>{{ result.total_count }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% endif %}
</div>

</body>
</html>