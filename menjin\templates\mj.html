<!DOCTYPE html>
{% load static %}

<html>
<head>
    <title>图书馆门禁通行记录查询系统</title>
    <link rel="stylesheet" href="{% static 'layui/css/layui.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}">
    <!-- Add the favicon link -->
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <style>
        .layui-nav {
            background: none;
            border: none;
            padding: 0;
        }
        .layui-nav .layui-nav-item {
            margin: 0 10px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .layui-nav .layui-nav-item a {
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
        }
        .layui-nav .layui-nav-item:hover a {
            background-color: #f2f2f2;
            color: #009688;
        }
        .layui-nav .layui-nav-item.layui-this a {
            background-color: #009688;
            color: white !important;
        }
        .layui-nav .layui-nav-item .layui-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body class="layui-layout-body">
    <!-- 登录状态 -->
    <div class="login-status" style="position: absolute; top: 20px; right: 20px; color: #666; z-index: 999;">
        {% if user.is_authenticated %}
            欢迎，{{ user.username }} | <a href="{% url 'logout' %}" style="color: #009688;">退出</a>
        {% else %}
            <a href="{% url 'login' %}" style="color: #009688;">登录</a>
        {% endif %}
    </div>
    <div class="layui-container">
        <!-- 系统总标题 -->
        <div class="layui-card" style="margin-bottom: 10px;">
            <div class="layui-card-header" style="background-color: #2F4056; text-align: center; padding: 15px;">
                <h1 style="color: white; margin: 0; font-size: 24px;">图书馆门禁查询系统</h1>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="layui-card" style="margin-bottom: 15px;">
            <div class="layui-card-body" style="padding: 10px;">
                <ul class="layui-nav" lay-filter="">
                    <li class="layui-nav-item layui-this">
                        <a href="{% url 'mj' %}">
                            <i class="layui-icon layui-icon-search"></i> 图书馆门禁通行记录查询系统
                        </a>
                    </li>
                    <li class="layui-nav-item">
                        <a href="{% url 'kq' %}">
                            <i class="layui-icon layui-icon-user"></i> 图书馆馆员考勤查询
                        </a>
                    </li>
                    <li class="layui-nav-item">
                        <a href="{% url 'stats' %}">
                            <i class="layui-icon layui-icon-chart"></i> 门禁通行人次统计
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 页面内容 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form method="post" action="{% url 'mj' %}" onsubmit="return validateForm()" class="layui-form">
                    {% csrf_token %}
                    <div class="layui-form-item">
                        <div class="layui-inline" style="width: 100%;">
                            <label class="layui-form-label" style="width: 80px;">起始日期:</label>
                            <div class="layui-input-inline" style="width: 180px;">
                                <input type="text" name="start_date" id="start_date" placeholder="例如：2024-01-01" value="{{ start_date }}" class="layui-input" required>
                            </div>
                            <label class="layui-form-label" style="width: 80px;">截止日期:</label>
                            <div class="layui-input-inline" style="width: 180px;">
                                <input type="text" name="end_date" id="end_date" placeholder="例如：2024-02-01" value="{{ end_date }}" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="width: 60px;">姓名:</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" name="name" id="name" value="{{ name }}" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="width: 80px;">证件号:</label>
                            <div class="layui-input-inline" style="width: 180px;">
                                <input type="text" name="id_number" id="id_number" value="{{ id_number }}" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 80px;">学院:</label>
                        <div class="layui-input-inline" style="width: 200px;">
                            <select name="category" id="category" class="layui-select">
                                <option value="" selected disabled>请选择（非必须）</option>
                                <option value="商学院">商学院</option>
                                <option value="师范学院">师范学院</option>
                                <option value="外国语学院">外国语学院</option>
                                <option value="数学与统计学院">数学与统计学院</option>
                                <option value="机械工程学院">机械工程学院</option>
                                <option value="材料工程学院">材料工程学院</option>
                                <option value="汽车工程学院">汽车工程学院</option>
                                <option value="生物与食品工程学院">生物与食品工程学院</option>
                                <option value="电子信息工程学院">电子信息工程学院</option>
                                <option value="电气与自动化工程学院">电气与自动化工程学院</option>
                                <option value="纺织服装与设计学院">纺织服装与设计学院</option>
                                <option value="计算机科学与工程学院">计算机科学与工程学院</option>
                                <option value="图书馆">图书馆</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-form-item">
                            <!-- 将 "layui-btn-normal" 替换为 "layui-btn-cyan" 统一颜色 -->
                            <button type="submit" class="layui-btn layui-btn-cyan">查询</button>
                            <!-- 下载按钮可添加自定义样式或使用 layui-btn-primary 但调整颜色，此处建议通过 CSS 覆盖 -->
                            <button type="submit" name="download_url" class="layui-btn layui-btn-primary download-btn">查询结果直接下载为Excel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if show_results %}
        <div class="layui-card">
            <div class="layui-card-body">
                {% if table_html %}
                    <fieldset class="layui-elem-field">
                        <legend>查询条件</legend>
                        <div class="layui-field-box">
                            <p>
                                {% if start_date %}
                                    <span class="layui-badge layui-bg-cyan">起始日期:</span> {{ start_date }}
                                {% endif %}
                                {% if end_date %}
                                    <span class="layui-badge layui-bg-cyan">截止日期:</span> {{ end_date }}
                                {% endif %}
                                {% if name %}
                                    <span class="layui-badge layui-bg-cyan">姓名:</span> {{ name }}
                                {% endif %}
                                {% if id_number %}
                                    <span class="layui-badge layui-bg-cyan">证件号:</span> {{ id_number }}
                                {% endif %}
                            </p>
                        </div>
                    </fieldset>
                    
                    <fieldset class="layui-elem-field">
                        <legend>查询结果</legend>
                        <div class="layui-field-box">
                            {{ table_html|safe }}
                        </div>
                    </fieldset>
                {% else %}
                    <div class="layui-elem-quote layui-quote-nm">
                        <p>没有符合查询条件的结果。</p>
                    </div>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="layui-card">
            <div class="layui-card-body">
                <blockquote class="layui-elem-quote layui-text-cyan">
                    <p><b>欢迎使用门禁记录查询系统！</b></p>
                </blockquote>
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">系统说明</h2>
                        <div class="layui-colla-content layui-show">
                            <p>本系统可查询所有人的图书馆门禁通行记录。</p>
                            <p>若查询超过一个月的记录，请点击第二个按钮下载后查看，否则结果太多页面无法显示。</p>
                            <p>起始日期为必填项，其他非必填，但不要自相矛盾。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="{% static 'jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'layui/layui.js' %}"></script>
    <script>
    layui.use(['form', 'layer', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var element = layui.element;

        $('#start_date').click(function() {
            if ($(this).val() == '') {
                $(this).val('2025-05-01');
            }
        });

        $('#end_date').click(function() {
            if ($(this).val() == '') {
                $(this).val('2025-05-05');
            }
        });
    });

    function validateForm() {
        // 新增日期逻辑顺序校验
        var startDate = new Date(document.getElementById("start_date").value);
        var endDate = new Date(document.getElementById("end_date").value);
        if (endDate < startDate) {
            layer.msg("结束日期不能早于起始日期", {icon: 2});
            return false;
        }
        var startDate = document.getElementById("start_date").value;
        var endDate = document.getElementById("end_date").value;
        var name = document.getElementById("name").value;
        var idNumber = document.getElementById("id_number").value;
        if (startDateStr === "" && endDateStr === "" && name === "" && idNumber === "") {
            layer.msg('请至少输入一个查询条件！', {icon: 5});
            return false;
        }
        return true;
    }
    </script>

<script>
    layui.use(['form', 'layer', 'laydate', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var element = layui.element;

        // 日期控件初始化
        laydate.render({
            elem: '#start_date'
        });
        laydate.render({
            elem: '#end_date'
        });

        // 表单验证
        form.verify({
            // 自定义验证规则
        });
    });
     </script>
</body>
</html>
<script>
$(document).ready(function() {
    $('form').on('submit', function(e) {
        if ($(e.originalEvent.submitter).attr('name') !== 'download_url') {
            e.preventDefault();
            var formData = $(this).serialize();
            $.ajax({
                url: $(this).attr('action'),
                type: $(this).attr('method'),
                data: formData,
                success: function(response) {
                    $('body').html(response);
                    // 防止刷新提示
                    window.history.pushState({}, '', window.location.href);
                },
                error: function() {
                    alert('提交失败，请重试。');
                }
            });
        }
    });
});
</script>