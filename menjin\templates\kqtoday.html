<!DOCTYPE html>
{% load static %}

<html>
<head>
    <title>今日馆员通行实时记录</title>
    <link rel="stylesheet" href="{% static 'layui/css/layui.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}">
    <!-- Add the favicon link -->
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
</head>
<body class="layui-layout-body">
    <!-- 登录状态 -->
    <div class="login-status" style="position: absolute; top: 20px; right: 20px; color: #666; z-index: 999;">
        {% if user.is_authenticated %}
            欢迎，{{ user.username }} | <a href="{% url 'logout' %}" style="color: #009688;">退出</a>
        {% else %}
            <a href="{% url 'login' %}" style="color: #009688;">登录</a>
        {% endif %}
    </div>
    <div class="layui-container">
        <div class="layui-card">
            <div class="layui-card-header layui-bg-green">
                <h1 class="layui-font-18">今日馆员通行实时记录</h1>
            </div>
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <a href="{% url 'kq' %}" class="layui-btn layui-btn-primary">点此返回</a>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-elem-quote layui-quote-nm">
                        <p>东湖：进（245、272、275、294），出（242、269、278、291）</p>
                        <p>东南：进（281、250、300），出（252、297）</p>
                    </div>
                </div>

                {% if kqtoday_results %}
                <div class="layui-form-item">
                    <div style="max-height: 1000px; overflow-y: auto;">
                        <table class="layui-table">
                            <thead>
                                <tr>
                                    <th>记录号</th>
                                    <th>时间</th>
                                    <th>证件号</th>
                                    <th>姓名</th>
                                    <th>通道</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in kqtoday_results %}
                                <tr>
                                    <td>{{ result.0 }}</td>
                                    <td>{{ result.1 }}</td>
                                    <td>{{ result.2 }}</td>
                                    <td>{{ result.3 }}</td>
                                    <td>{{ result.4 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="{% static 'jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'layui/layui.js' %}"></script>
    <script>
    layui.use(function(){
        var layer = layui.layer;
        // 页面加载提示
        layer.msg('页面加载完成', {icon: 1});
    });
    </script>
</body>
</html>
