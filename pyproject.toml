[project]
name = "menjin"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "cx-oracle>=8.3.0",
    "django>=4.2.0",
    "django-widget-tweaks>=1.5.0",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "pymysql>=1.1.1",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.3",
    "whitenoise>=6.9.0",
]

[[tool.uv.index]]
url = "https://pypi.mirrors.ustc.edu.cn/simple/"
default = true
