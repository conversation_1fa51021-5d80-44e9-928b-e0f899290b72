<!DOCTYPE html>
{% load static %}

<html>
<head>
    <title>图书馆馆员考勤查询</title>
    <link rel="stylesheet" href="{% static 'layui/css/layui.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}">
    <style>
        .layui-nav {
            background: none;
            border: none;
            padding: 0;
        }
        .layui-nav .layui-nav-item {
            margin: 0 10px;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .layui-nav .layui-nav-item a {
            color: #333;
            padding: 5px 20px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
        }
        .layui-nav .layui-nav-item:hover a {
            background-color: #f2f2f2;
            color: #5FB878;
        }
        .layui-nav .layui-nav-item.layui-this a {
            background-color: #5FB878;
            color: white !important;
        }
        .layui-nav .layui-nav-item .layui-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body class="layui-layout-body">
    <!-- 登录状态 -->
    <div class="login-status" style="position: absolute; top: 20px; right: 20px; color: #666; z-index: 999;">
        {% if user.is_authenticated %}
            欢迎，{{ user.username }} | <a href="{% url 'logout' %}" style="color: #009688;">退出</a>
        {% else %}
            <a href="{% url 'login' %}" style="color: #009688;">登录</a>
        {% endif %}
    </div>
    <div class="layui-container">
        <!-- 系统总标题 -->
        <div class="layui-card" style="margin-bottom: 10px;">
            <div class="layui-card-header" style="background-color: #2F4056; text-align: center; padding: 15px;">
                <h1 style="color: white; margin: 0; font-size: 24px;">图书馆门禁查询系统</h1>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="layui-card" style="margin-bottom: 15px;">
            <div class="layui-card-body" style="padding: 10px;">
                <ul class="layui-nav" lay-filter="">
                    <li class="layui-nav-item">
                        <a href="{% url 'mj' %}">
                            <i class="layui-icon layui-icon-search"></i> 图书馆门禁通行记录查询系统
                        </a>
                    </li>
                    <li class="layui-nav-item layui-this">
                        <a href="{% url 'kq' %}">
                            <i class="layui-icon layui-icon-user"></i> 图书馆馆员考勤查询
                        </a>
                    </li>
                    <li class="layui-nav-item">
                        <a href="{% url 'stats' %}">
                            <i class="layui-icon layui-icon-chart"></i> 门禁通行人次统计
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 页面内容 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form">
                    <div class="layui-form-item">
                        <a href="{% url 'kqtoday' %}" class="layui-btn layui-btn-primary">查看今日馆员通行记录</a>
                    </div>
                    
                    <form method="post" action="{% url 'kq' %}" onsubmit="return validateForm()" class="layui-form">
                        {% csrf_token %}
                        <div class="layui-form-item">
                            <div class="layui-inline" style="width: 100%;">
                                <label class="layui-form-label" style="width: 80px;">起始日期:</label>
                                <div class="layui-input-inline" style="width: 180px;">
                                    <input type="text" name="start_date" id="start_date" placeholder="例如：2024-01-01" value="{{ start_date }}" class="layui-input" required>
                                </div>
                                <label class="layui-form-label" style="width: 80px;">截止日期:</label>
                                <div class="layui-input-inline" style="width: 180px;">
                                    <input type="text" name="end_date" id="end_date" placeholder="例如：2024-02-01" value="{{ end_date }}" class="layui-input">
                                </div>
                                <label class="layui-form-label" style="width: 60px;">姓名:</label>
                                <div class="layui-input-inline" style="width: 120px;">
                                    <input type="text" name="name" id="name" value="{{ name }}" class="layui-input">
                                </div>
                                <label class="layui-form-label" style="width: 80px;">证件号:</label>
                                <div class="layui-input-inline" style="width: 180px;">
                                    <input type="text" name="id_number" id="id_number" value="{{ id_number }}" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <button type="submit" class="layui-btn layui-bg-green">查询</button>
                            <button type="submit" name="download_url" class="layui-btn layui-btn-primary">查询结果直接下载为Excel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {% if show_results %}
        <div class="layui-card">
            <div class="layui-card-body">
                {% if table_html %}
                    <fieldset class="layui-elem-field">
                        <legend>查询条件</legend>
                        <div class="layui-field-box">
                            <p>
                                {% if start_date %}
                                    <span class="layui-badge layui-bg-blue">起始日期:</span> {{ start_date }}
                                {% endif %}
                                {% if end_date %}
                                    <span class="layui-badge layui-bg-blue">截止日期:</span> {{ end_date }}
                                {% endif %}
                                {% if name %}
                                    <span class="layui-badge layui-bg-blue">姓名:</span> {{ name }}
                                {% endif %}
                                {% if id_number %}
                                    <span class="layui-badge layui-bg-blue">证件号:</span> {{ id_number }}
                                {% endif %}
                            </p>
                        </div>
                    </fieldset>
                    
                    <fieldset class="layui-elem-field">
                        <legend>查询结果</legend>
                        <div class="layui-field-box">
                            {{ table_html|safe }}
                        </div>
                    </fieldset>
                {% else %}
                    <div class="layui-elem-quote layui-quote-nm">
                        <p>没有符合查询条件的结果。</p>
                    </div>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="layui-card">
            <div class="layui-card-body">
                <blockquote class="layui-elem-quote">
                    <p><b>欢迎使用馆员考勤查询系统！</b></p>
                </blockquote>
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">系统说明</h2>
                        <div class="layui-colla-content layui-show">
                            <p>1.本系统可查询馆员自2021年1月1日起，每天首次和最后一次通行记录。系统后台每天半夜会对门禁中馆员当天的通行记录进行整理统计。</p>
                            <p>2.点击标题行可刷新页面，点击"查看今日馆员通行记录"可查看当天馆员通行记录。</p>
                            <p>3.起始日期为必填项；其他检索条件可不填，若填写，不要自相矛盾，否则无结果。</p>
                            <p>4.设置好查询条件，再点击第二个按钮会把结果下载为一个Excel文件。</p>
                            <br>
                            <p>异常情况字段说明：迟到或早退超45分钟的，会写成"上班时间"。当天只有一次刷卡记录的，记为"当天刷一次"。</p>
                            <p>周一到周五会比对完整馆员名单，列出无刷卡记录的人员。（该项数据不是实时检索，从2024年5月7晚上开始进行每天自动统计。在此之前无此项数据。）</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="{% static 'jquery-3.6.0.min.js' %}"></script>
    <script src="{% static 'layui/layui.js' %}"></script>
    <script>
    layui.use(['form', 'layer', 'jquery', 'element'], function() {
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.jquery;
        var element = layui.element;
    
        $('#start_date').click(function() {
            if ($(this).val() == '') {
                $(this).val('2025-05-01');
            }
        });
        $('#end_date').click(function() {
            if ($(this).val() == '') {
                $(this).val('2025-05-10');
            }
        });
    
        // 阻止表单默认提交行为，使用 AJAX 提交
        $('form').on('submit', function(e) {
            if (!$(e.originalEvent.submitter).is('[name="download_url"]')) {
                e.preventDefault();
                var formData = $(this).serialize();
                $.ajax({
                    url: '{% url "kq" %}',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        // 假设服务器返回的 HTML 可以直接替换页面内容
                        $('body').html(response);
                    },
                    error: function() {
                        layer.msg('查询失败，请重试', {icon: 2});
                    }
                });
            }
        });
    });

    function validateForm() {
        // 新增日期逻辑顺序校验
        var startDate = new Date(document.getElementById("start_date").value);
        var endDate = new Date(document.getElementById("end_date").value);
        if (endDate < startDate) {
            layer.msg("结束日期不能早于起始日期", {icon: 2});
            return false;
        }
        var startDateStr = document.getElementById("start_date").value;
        var endDateStr = document.getElementById("end_date").value;
        var name = document.getElementById("name").value;
        var idNumber = document.getElementById("id_number").value;
        if (startDateStr === "" && endDateStr === "" && name === "" && idNumber === "") {
            layer.msg('请至少输入一个查询条件！', {icon: 5});
            return false;
        }
        return true;
    }
    </script>
  

    <script>
    layui.use(['form', 'layer', 'laydate', 'element'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var element = layui.element;
        
        // 日期控件初始化
        laydate.render({
            elem: '#start_date'
        });
        laydate.render({
            elem: '#end_date'
        });
        
        // 表单验证
        form.verify({
            // 自定义验证规则
        });
    });
    </script>
    
</body>
</html>