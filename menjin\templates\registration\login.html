<!DOCTYPE html>
{% load static %}
{% load widget_tweaks %} <!-- 假设使用了 widget_tweaks 库 -->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}图书馆门禁查询系统{% endblock %}</title>
    <link rel="stylesheet" href="{% static 'layui/css/layui.css' %}">
    <style>
        body {
            padding: 1em;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: flex-start;  /* 修改为顶部对齐 */
            min-height: 100vh;
            padding-top: 20px;  /* 增加顶部内边距 */
        }
        .layui-container {
            width: 400px;
            max-width: 100%;
            margin: 20px auto 0;  /* 增加顶部外边距 */
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
        }
        .card {
            padding: 1.5em;
            margin-bottom: 1em;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: white;
        }
        .error {
            color: #ff4444;
        }
        .layui-card-header {
            font-size: 24px; /* 增大系统登录字体 */
        }
        .layui-input {
            height: 40px; /* 增大文本框高度 */
            font-size: 16px; /* 增大文本框字体大小 */
        }
    </style>
</head>
<body>
   <div class="layui-container">
    <div class="layui-card-header layui-font-30" style="text-align: center; padding: 20px 0; font-size: 28px;">
        图书馆门禁查询系统
    </div>
    <div class="layui-card">
        <div class="layui-card-header">系统登录</div>
        <div class="layui-card-body">
            <form method="post" class="layui-form">
                {% csrf_token %} <br>
                <!-- 使用自定义标签替换英文标签 -->
                <p><label for="id_username">用户名:</label> {% render_field form.username class='layui-input' %}</p>
<p><label for="id_password">密码:</label> {% render_field form.password class='layui-input' %}</p>
               <br> <button type="submit" class="layui-btn layui-btn-fluid" style="width: 100px; height: 40px;">登录</button>
            </form>
        </div>
    </div>
</div>
</body>
<footer style="
    text-align: center;
    margin: 30px auto;
    color: #666;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
">
    <p>Copyright © 图书馆技术部 2025</p>
</footer>
</html>
