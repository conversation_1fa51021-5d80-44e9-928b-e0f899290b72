/* 以下为 style-index.css 内容（首页样式） */
html, body {
	margin: 0;
	overflow: auto;  /* 修改为auto，允许滚动条 */
	width: 100%;
	height: 100%;
	background: black;
	background: linear-gradient(to bottom, #000000 0%, #5788fe 100%);
	font-family:'Questrial','Noto Serif SC' ;
}

.filter {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background: #fe5757;
	animation: colorChange 30s ease-in-out infinite;
	animation-fill-mode: both;
	mix-blend-mode: overlay;
}

@keyframes colorChange {
	0%, 100% {
		opacity: 0;
	}
	50% {
		opacity: .9;
	}
}

.landscape {
	position: absolute;
	bottom: 0px;
	left: 0;
	width: 100%;
	height: 100%;	
	background-image: url('');
	background-size: 1000px 250px;
	background-repeat: repeat-x;
	background-position: center bottom;
}
.content{
	-moz-user-select: none;
-webkit-user-select: none;
-ms-user-select: none;
-khtml-user-select: none;
user-select: none;
	text-align: center;
	color: #fff;
    position: absolute;
    left: 50%;
    top: 45%;
	transform: translate(-50%,-50%);
	z-index:9999
}
.content .title{
	margin: 15px 0;
    font-size: 2.5em;
    letter-spacing: 4px;
    color: #FFF;
}
.content .hr{
	width: 50%;
	margin: 20px auto;
	border: none;
	border-top: 1px solid rgba(255, 255, 255, 1);
	height: 1px;
}
.content .discription{
    font-size: 20px;
    margin: 20px;
}
.cover-navigation {
	margin: 30px;
}

nav {
    display: inline-block;
    position: relative;
}
.navigation {
    display: inline-block;
    position: relative;
    margin: 0;
    list-style-type: none;
}
.navigation__item {
    display: inline-block;
    line-height: 1em;
    padding: 1em 0;
}
.navigation__item a {
    position: relative;
    color: #FFF;
    opacity: .8;
	transition: all .3s;
	padding: 10px 20px;
    border: 1px solid #fff;
    border-radius: 20px;
    font-size: .9em;
    font-weight: bold;
    letter-spacing: 1px;
    text-shadow: none;
	-webkit-font-smoothing: antialiased;
	text-decoration: none;
}
.navigation__item a:hover {
    color: #FFF;
    background: #FF7F00;
    border-color: #FF7F00;
    opacity: 1;
    transition: all .3s;
}
ol, ul {
	list-style: none;
	margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}
.icp{
	margin: 20px;
}
.icpnum{
	color: #fff;
	text-decoration: none;
}

/* 以下为 styles.css 内容（通用样式） */
table {
	border-collapse: collapse;
	width: 60%;
	font-family: Arial, sans-serif;
	align: center;
	margin-left:auto;
	margin-right:auto;
}

th, td {
	border: 1px solid #ddd;
	text-align: left;
	padding: 8px;
}

tr:nth-child(even) {
	background-color: #f2f2f2;
}

th {
	background-color: #4CAF50;
	color: white;
}

h1 {
	text-align: center;
}

input[type=text], 
input[type=email], 
input[type=password] {
  border: none;
  border-bottom: 2px solid #ccc;
  font-size: 16px;
  padding: 10px 0;
  margin-bottom: 20px;
  width: 150px;
  outline: none;
}

input[type=text]:focus, 
input[type=email]:focus, 
input[type=password]:focus {
  border-bottom: 2px solid #3c97f7;
}

button {
  background-color: #3c97f7;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 32px;
  padding: 20px 40px;
  cursor: pointer;
}

button:hover {
  background-color: #0e71c8;
}

input[type=submit] {
  background-color: #4CAF50; /*背景色*/
  border: none; /*边框*/
  color: white; /*字体颜色*/
  padding: 10px 20px; /*内边距*/
  text-align: center; /*文字居中*/
  text-decoration: none; /*去掉超链接下划线*/
  display: inline-block; 
  font-size: 16px; /*字体大小*/
  margin: 4px 2px; /*外边距*/
  cursor: pointer; /*光标指针*/
}

.divcss5-cent {margin:0auto;text-align:center;}
.divcss5-left {margin-left: 300px;;text-align:left;}
    
    /* 未访问的链接样式 */
    a {
      color: #333;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    /* 鼠标悬停时的链接样式 */
    a:hover {
      color: #FF0000;
    }

    /* 链接被点击后的样式 */
    a:active {
      color: #0000FF;
    }

/* 以下为 styles2.css 内容（补充通用样式） */
th {
	background-color: #4c8baf;
	color: white;
}

input[type=submit] {
  background-color: #4c8baf; /*背景色*/
}

/* 渐变背景示例 */
.layui-bg-gradient {
    background: linear-gradient(45deg, #1E9FFF, #40E0D0); /* 青蓝到水绿渐变 */
    color: white;
    border: none; /* 可选：移除默认边框 */
}

/* 添加表格容器滚动条 */
.layui-field-box {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: auto;
  margin: 15px 0;
  border: 1px solid #eee;
}

/* 固定表头 */
.layui-table thead {
  position: sticky;
  top: 0;
  background-color: #FFF;
  z-index: 1;
}

/* 优化表格显示 */
.layui-table {
  table-layout: auto;
  min-width: 100%;
  white-space: nowrap;
}
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #333;
}